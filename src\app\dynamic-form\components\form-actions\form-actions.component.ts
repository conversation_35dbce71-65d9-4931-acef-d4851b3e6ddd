import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-form-actions',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './form-actions.component.html',
  styleUrl: './form-actions.component.scss'
})
export class FormActionsComponent {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() isTenantBasedFlag: boolean = false;
  @Input() authorizeNumber: number = 1;
  @Input() isViewMode: boolean = false;
  @Input() showSuccessPopup: boolean = false;
  @Input() successMessage: string = '';
  @Input() errorMessage: string = '';
  @Input() isLoading: boolean = false;
  @Input() validationResult: any;
  @Input() fields: any[] = [];

  @Output() submissionSuccess = new EventEmitter<boolean>();
  @Output() errorMessageChange = new EventEmitter<string>();
  @Output() isLoadingChange = new EventEmitter<boolean>();
  @Output() showSuccessPopupChange = new EventEmitter<boolean>();
  @Output() successMessageChange = new EventEmitter<string>();
  @Output() validationResultChange = new EventEmitter<any>();
  @Output() goBackRequested = new EventEmitter<void>();
  @Output() setFormReadonly = new EventEmitter<boolean>();
  @Output() populateForm = new EventEmitter<any>();
  @Output() populateDefaultFields = new EventEmitter<any[]>();
  @Output() setViewMode = new EventEmitter<boolean>();

  private http = inject(HttpClient);

  authorizeRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    const id = this.form.get("ID")?.value;
    const tablesApiId = this.extractTablesApiId(this.tableName);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/authorize`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
      authorizeNumber: this.authorizeNumber
    };
    this.http.put(apiUrl, {}, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopupChange.emit(true);
          this.successMessageChange.emit("Record authorized successfully!");
          this.setViewMode.emit(false);
          this.setFormReadonly.emit(false);
          this.goBackRequested.emit();
          setTimeout(() => {
            this.showSuccessPopupChange.emit(false);
          }, 20000);
        } else {
          this.errorMessageChange.emit(response.message || "Authorization failed");
        }
      },
      error: (error) => {
        this.errorMessageChange.emit("An error occurred during authorization");
      },
      complete: () => this.isLoadingChange.emit(false)
    });
  }

  onSubmit() {
    this.errorMessageChange.emit("");

    if (this.form.valid) {
      const tablesApiId = this.extractTablesApiId(this.tableName);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records`;
      const formData = this.buildFormData(this.form.getRawValue());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.showSuccessPopupChange.emit(true);
            this.successMessageChange.emit("Record submitted successfully!");
            this.goBackRequested.emit();
            setTimeout(() => {
              this.showSuccessPopupChange.emit(false);
            }, 20000);
          } else if (response.status === "error") {
            this.errorMessageChange.emit(response.message[0].error || "An error occurred while submitting the form");
          }
        },
        error: (error: any) => {
          if (error.error && error.error.message) {
            this.errorMessageChange.emit(error.error.message[0].error);
          } else {
            this.errorMessageChange.emit("An unexpected error occurred while submitting the form");
          }
        }
      });
    }
  }

  validateRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    if (this.form.valid) {
      const tablesApiId = this.extractTablesApiId(this.tableName);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/validate`;
      const formData = this.buildFormData(this.form.getRawValue());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.validationResultChange.emit(response.data);
            this.populateForm.emit(response.data);
            // Handle defaultFields even in error response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields.emit(response.defaultFields);
            }
          } else if (response.status === "error") {
            this.validationResultChange.emit(response.data);
            this.errorMessageChange.emit(response.message[0].error || "An error occurred during validation");
            this.populateForm.emit(response.data);
            // Handle defaultFields if present in validation response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields.emit(response.defaultFields);
            }
          }
        },
        error: (error: any) => {
          this.validationResultChange.emit(error.error?.data || null);
          this.errorMessageChange.emit(error.error?.message[0].error || "An unexpected error occurred during validation");

          if (error.error?.data) {
            this.populateForm.emit(error.error.data);
          }
        },
        complete: () => this.isLoadingChange.emit(false)
      });
    }
  }

  closeSuccessPopup() {
    this.showSuccessPopupChange.emit(false);
  }

  onRejectRecord() {
    // Placeholder for reject functionality
    console.log('Reject record functionality not implemented yet');
  }

  onDeleteRecord() {
    // Placeholder for delete functionality
    console.log('Delete record functionality not implemented yet');
  }

  private buildFormData(data: any): any {
    const result: { [key: string]: any } = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        if (value === null || value === undefined || value === "") continue;

        if (Array.isArray(value)) {
          const field = this.fields.find(field => field.fieldName === key);
          if (field && field.isMulti) {
            if (typeof value[0] === 'object' && value[0][field.fieldName] !== undefined) {
              result[key] = value.map((item: any) => item[field.fieldName]);
            } else {
              result[key] = value;
            }
          } else {
            const nestedData = value.map((item: any) => {
              if (item.fieldsToAppear) {
                return item.fieldsToAppear;
              } else {
                return this.buildFormData(item);
              }
            });
            if (nestedData.length > 0) {
              result[key] = nestedData;
            }
          }
        } else if (typeof value === "object") {
          const nestedObject = this.buildFormData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[key] = nestedObject;
          }
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }

  // Helper method to extract part before comma for tables API calls
  private extractTablesApiId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }
}
