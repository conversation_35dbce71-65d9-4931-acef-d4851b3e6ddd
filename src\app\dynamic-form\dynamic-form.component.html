

<!-- Form Actions Component -->
<app-form-actions #formActions
  [form]="form"
  [tableName]="tableName"
  [screenName]="screenName"
  [isTenantBasedFlag]="isTenantBasedFlag"
  [authorizeNumber]="authorizeNumber"
  [isViewMode]="isViewMode"
  [showSuccessPopup]="showSuccessPopup"
  [successMessage]="successMessage"
  [errorMessage]="errorMessage"
  [isLoading]="isLoading"
  [validationResult]="validationResult"
  [fields]="fields"
  (submissionSuccess)="onSubmissionSuccess($event)"
  (errorMessageChange)="onErrorMessageChange($event)"
  (isLoadingChange)="onIsLoadingChange($event)"
  (showSuccessPopupChange)="onShowSuccessPopupChange($event)"
  (successMessageChange)="onSuccessMessageChange($event)"
  (validationResultChange)="onValidationResultChange($event)"
  (goBackRequested)="onGoBackRequested()"
  (setFormReadonly)="onSetFormReadonly($event)"
  (populateForm)="onPopulateForm($event)"
  (populateDefaultFields)="onPopulateDefaultFields($event)"
  (setViewMode)="onSetViewMode($event)">
</app-form-actions>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <app-initial-input
      [form]="form"
      [tableName]="tableName"
      [screenName]="screenName"
      [showValidation]="showValidation"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()"
      (validationChange)="onValidationChange($event)">
    </app-initial-input>
  }

  @if (errorMessage) {
    <div class="error-message">{{ errorMessage }}</div>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form">
      <app-form-header
        [form]="form"
        [isViewMode]="isViewMode"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        (toggleViewMode)="toggleViewMode()"
        (submitForm)="onFormSubmit()"
        (validateRecord)="onFormValidate()"
        (authorizeRecord)="onFormAuthorize()"
        (goBack)="goBack()"
        (rejectRecord)="onFormReject()"
        (deleteRecord)="onFormDelete()">
      </app-form-header>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->
      <div class="form-grid" [ngClass]="'columns-' + columnCount">
        <div class="form-column" *ngFor="let column of columns">
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

            <!-- 🔸 Non-Grouped Fields -->
            @if (!field.Group) {

                <!-- 🔸 Regular Field Component (non-multi) -->
                @if (!field.isMulti) {
                  <app-regular-field
                    [field]="field"
                    [form]="form"
                    [isViewMode]="isViewMode"
                    [fields]="fields"
                    (fieldValueChange)="onFieldValueChange($event)">
                  </app-regular-field>
                }

                <!-- 🔸 Multi-Field (Non-Grouped) -->
                @if (field.isMulti) {
                  <div [formArrayName]="field.fieldName">
                    @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                      <div [formGroupName]="j" class="form-field is-multi">
                        @if (field.foreginKey) {
                          <app-regular-field
                            [field]="field"
                            [form]="$any(control)"
                            [isViewMode]="isViewMode"
                            [fields]="fields"
                            [multiIndex]="j + 1"
                            (fieldValueChange)="onFieldValueChange($event)">
                          </app-regular-field>
                        } @else {
                          <label>{{ field.fieldName }} ({{ j + 1 }})
                            @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                          </label>

                          <div class="multi-input-container">
                            <div class="multi-input">
                              <!-- Regular input fields for non-foreign key multi-fields -->
                              @if (field.type === 'string') {
                                <input [formControlName]="field.fieldName" type="text"
                                  [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'int') {
                                <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'boolean') {
                                <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'date') {
                                <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'double') {
                                <input [formControlName]="field.fieldName" type="number"
                                  step="00.50" [disabled]="field.noInput" />
                              }
                            </div>

                            <div class="multi-buttons">
                              @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                                <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              }

                              @if (!isViewMode && !field.noInput) {
                                <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                                  <mat-icon>add</mat-icon>
                                </button>
                              }
                            </div>
                          </div>
                        }
                      </div>
                    }
                  </div>
                }
            }

            <!-- 🔸 Grouped Fields -->
            @if (field.Group && isFirstFieldInParentGroup(field)) {
              @let parsed = parseGroupPath(field.Group);
              @if (parsed.parent) {
                <div [formArrayName]="parsed.parent" class="grouped-field-section">
                  <h3>{{ parsed.parent }}</h3>
                  @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                    <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                      @if (isRowView) {
                        <!-- Row View: All fields in a single table-like row -->
                        <div class="row-view-table-container">
                          <!-- Parent group fields -->
                          @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                            @if (!groupField.isMulti) {
                              <div class="row-view-table-cell">
                                @if (groupField.foreginKey) {
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(group)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                } @else {
                                  <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                                    @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                  </label>
                                  <!-- Regular input fields for non-foreign key grouped fields -->
                                  @if (groupField.type === 'string') {
                                    <input [formControlName]="groupField.fieldName" type="text" [placeholder]="(groupField.label?.trim() || groupField.fieldName)" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'int') {
                                    <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'boolean') {
                                    <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'date') {
                                    <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'double') {
                                    <input [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                                  }
                                }
                              </div>
                            }

                            <!-- Multi-fields for parent group -->
                            @if (groupField.isMulti) {
                              <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                                <label>{{ groupField.fieldName }}
                                  @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                </label>
                                @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                  <div [formGroupName]="l" class="row-view-multi-item">
                                    <div class="row-view-multi-input">
                                      @if (groupField.type === 'string') {
                                        <input [formControlName]="groupField.fieldName" type="text"
                                          [placeholder]="(groupField.label?.trim() || groupField.fieldName) + ' (' + (l + 1) + ')'" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'int') {
                                        <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'boolean') {
                                        <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'date') {
                                        <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'double') {
                                        <input [formControlName]="groupField.fieldName" type="number" step="0.01" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.foreginKey) {
                                        <app-regular-field
                                          [field]="groupField"
                                          [form]="$any(multiControl)"
                                          [isViewMode]="isViewMode"
                                          [fields]="fields"
                                          [multiIndex]="l + 1"
                                          (fieldValueChange)="onFieldValueChange($event)">
                                        </app-regular-field>
                                      }
                                    </div>
                                    <div class="row-view-multi-buttons">
                                      @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                        <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                          <mat-icon>delete</mat-icon>
                                        </button>
                                      }
                                      @if (!isViewMode && !groupField.noInput) {
                                        <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                          <mat-icon>add</mat-icon>
                                        </button>
                                      }
                                    </div>
                                  </div>
                                }
                              </div>
                            }
                          }

                          <!-- Nested group fields -->
                          @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                            @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                              @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {
                                @if (!nestedField.isMulti) {
                                  <div class="row-view-table-cell" [formArrayName]="childGroup" [formGroupName]="n">
                                    @if (nestedField.foreginKey) {
                                      <app-regular-field
                                        [field]="nestedField"
                                        [form]="$any(nestedGroup)"
                                        [isViewMode]="isViewMode"
                                        [fields]="fields"
                                        (fieldValueChange)="onFieldValueChange($event)">
                                      </app-regular-field>
                                    } @else {
                                      <label>{{ nestedField.fieldName }} @if (nestedField.mandatory) {<span>*</span>}
                                        @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                      </label>
                                      <!-- Regular input fields for non-foreign key nested fields -->
                                      @if (nestedField.type === 'string') {
                                        <input [formControlName]="nestedField.fieldName" type="text" [placeholder]="(nestedField.label?.trim() || nestedField.fieldName)" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'int') {
                                        <input [formControlName]="nestedField.fieldName" type="number" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'boolean') {
                                        <input [formControlName]="nestedField.fieldName" type="checkbox" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'date') {
                                        <input [formControlName]="nestedField.fieldName" type="date" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'double') {
                                        <input [formControlName]="nestedField.fieldName" type="number" step="00.50" [disabled]="nestedField.noInput" />
                                      }
                                    }
                                  </div>
                                }

                                <!-- Multi-fields for nested groups -->
                                @if (nestedField.isMulti) {
                                  <div class="row-view-table-cell-multi" [formArrayName]="childGroup" [formGroupName]="n">
                                    <div [formArrayName]="nestedField.fieldName">
                                      <label>{{ nestedField.fieldName }}
                                        @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                      </label>
                                      @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                        <div [formGroupName]="m" class="row-view-multi-item">
                                          <div class="row-view-multi-input">
                                            @if (nestedField.type === 'string') {
                                              <input [formControlName]="nestedField.fieldName" type="text"
                                                [placeholder]="(nestedField.label?.trim() || nestedField.fieldName) + ' (' + (m + 1) + ')'" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'int') {
                                              <input [formControlName]="nestedField.fieldName" type="number" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'boolean') {
                                              <input [formControlName]="nestedField.fieldName"
                                                type="checkbox" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'date') {
                                              <input [formControlName]="nestedField.fieldName"
                                                type="date" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'double') {
                                              <input [formControlName]="nestedField.fieldName"
                                                type="number" step="0.01" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.foreginKey) {
                                              <app-regular-field
                                                [field]="nestedField"
                                                [form]="$any(multiControl)"
                                                [isViewMode]="isViewMode"
                                                [fields]="fields"
                                                [multiIndex]="m + 1"
                                                (fieldValueChange)="onFieldValueChange($event)">
                                              </app-regular-field>
                                            }
                                          </div>
                                          <div class="row-view-multi-buttons">
                                            @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                <mat-icon>delete</mat-icon>
                                              </button>
                                            }
                                            @if (!isViewMode && !nestedField.noInput) {
                                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                <mat-icon>add</mat-icon>
                                              </button>
                                            }
                                          </div>
                                        </div>
                                      }
                                    </div>
                                  </div>
                                }
                              }

                              <!-- Nested group control buttons -->
                              <div class="row-view-table-actions" [formArrayName]="childGroup" [formGroupName]="n">
                                @if (!isViewMode) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                    <mat-icon>content_copy</mat-icon>
                                  </button>
                                }
                              </div>
                            }
                          }

                          <!-- Main group action buttons -->
                          <div class="row-view-table-actions">
                            @if (!isViewMode) {
                              <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                                <mat-icon>delete</mat-icon>
                              </button>
                              <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                                <mat-icon>add</mat-icon>
                              </button>
                              <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                                <mat-icon>content_copy</mat-icon>
                              </button>
                            }
                          </div>
                        </div>
                      } @else {
                        <!-- Nested View: Original layout -->
                        <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">

                        <!-- Direct fields of parent group -->
                        @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {

                        @if (!groupField.isMulti) {
                          <div class="form-field">
                            @if (groupField.foreginKey) {
                              <app-regular-field
                                [field]="groupField"
                                [form]="$any(group)"
                                [isViewMode]="isViewMode"
                                [fields]="fields"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            } @else {
                              <app-regular-field
                                [field]="groupField"
                                [form]="$any(group)"
                                [isViewMode]="isViewMode"
                                [fields]="fields"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            }
                          </div>
                        }

                        @if (groupField.isMulti) {
                          <div [formArrayName]="groupField.fieldName">
                            <h4>{{ groupField.fieldName }}
                              @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                            </h4>

                            @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                              <div [formGroupName]="l" class="form-field">
                                @if (!groupField.foreginKey) {
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(multiControl)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    [multiIndex]="l + 1"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                }

                                @if (groupField.foreginKey) {
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(multiControl)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    [multiIndex]="l + 1"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                }

                                @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                }

                                @if (!isViewMode && !groupField.noInput) {
                                  <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                }
                              </div>
                            }
                          </div>
                        }
                      }

                      <!-- Nested subgroups -->
                      @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                        <div [formArrayName]="childGroup" [class]="isRowView ? 'nested-group-section row-view-nested-seamless' : 'nested-group-section'">
                          <h4 [class.row-view-hidden]="isRowView">{{ childGroup }}</h4>
                          @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                            <div [formGroupName]="n" [class]="isRowView ? 'form-grid nested-field row-view-nested-field-seamless' : 'form-grid nested-field'">
                              <div [class]="isRowView ? 'nested-group-fields row-view-nested-fields-seamless' : 'nested-group-fields'">
                                @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {

                                  @if (!nestedField.isMulti) {
                                    <div class="form-field">
                                      <app-regular-field
                                        [field]="nestedField"
                                        [form]="$any(nestedGroup)"
                                        [isViewMode]="isViewMode"
                                        [fields]="fields"
                                        (fieldValueChange)="onFieldValueChange($event)">
                                      </app-regular-field>
                                    </div>
                                  }

                                  @if (nestedField.isMulti) {
                                    <div [formArrayName]="nestedField.fieldName">
                                      <h5>{{ nestedField.fieldName }}
                                        @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                      </h5>

                                      @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                        <div [formGroupName]="m" class="form-field is-multi">
                                          <div class="multi-input-container">
                                            <div class="multi-input">
                                              <app-regular-field
                                                [field]="nestedField"
                                                [form]="$any(multiControl)"
                                                [isViewMode]="isViewMode"
                                                [fields]="fields"
                                                [multiIndex]="m + 1"
                                                (fieldValueChange)="onFieldValueChange($event)">
                                              </app-regular-field>
                                            </div>

                                            <div class="multi-buttons">
                                              @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                  <mat-icon>delete</mat-icon>
                                                </button>
                                              }

                                              @if (!isViewMode && !nestedField.noInput) {
                                                <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                  <mat-icon>add</mat-icon>
                                                </button>
                                              }
                                            </div>
                                          </div>
                                        </div>
                                      }
                                    </div>
                                  }
                                }

                                <!-- Nested group control buttons -->
                                <div class="nested-group-buttons">
                                  @if (!isViewMode) {
                                    <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                      <mat-icon>delete</mat-icon>
                                    </button>
                                  }
                                  @if (!isViewMode) {
                                    <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                      <mat-icon>add</mat-icon>
                                    </button>
                                  }
                                  @if (!isViewMode) {
                                    <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                      <mat-icon>content_copy</mat-icon>
                                    </button>
                                  }
                                </div>
                              </div>
                            </div>
                          }
                        </div>
                      }

                        <!-- Group control buttons for nested view -->
                        <div class="group-buttons">
                          @if (!isViewMode) {
                            <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                              <mat-icon>delete</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                              <mat-icon>add</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                              <mat-icon>content_copy</mat-icon>
                            </button>
                          }
                        </div>
                      </div>
                      }

                    </div>
                  }
                </div>
              }
            }
          }
      
         </ng-container>
    </div>
  </div>
      </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
