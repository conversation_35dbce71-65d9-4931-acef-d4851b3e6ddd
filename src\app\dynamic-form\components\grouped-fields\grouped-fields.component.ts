import { Component, Input, Output, EventE<PERSON>ter, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray, FormBuilder, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

/**
 * Grouped Fields Component
 *
 * Handles all grouped field functionality including:
 * - Group lifecycle management (create, add, remove, clone)
 * - Nested group support with pipe notation (parent|child)
 * - Multi-field handling within groups
 * - Group validation and form control management
 * - Dynamic group operations and UI actions
 * - Row view and nested view rendering modes
 */
@Component({
  selector: 'app-grouped-fields',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatI<PERSON>Module,
    MatTooltipModule,
    RegularFieldComponent
  ],
  templateUrl: './grouped-fields.component.html',
  styleUrl: './grouped-fields.component.scss'
})
export class GroupedFieldsComponent implements OnInit, OnDestroy {
  // Input properties
  @Input() field!: any; // The field that triggers group rendering
  @Input() form!: FormGroup; // Parent form reference
  @Input() fields: any[] = []; // All form fields for group detection
  @Input() isViewMode: boolean = false; // Read-only mode flag
  @Input() isRowView: boolean = false; // Row view vs nested view toggle

  // Output events
  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();
  @Output() groupValueChange = new EventEmitter<{groupName: string, value: any}>();
  @Output() groupValidationChange = new EventEmitter<{groupName: string, isValid: boolean}>();

  // Internal properties
  private fb = inject(FormBuilder);
  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Initialize group if not already present
    this.initializeGroupIfNeeded();
  }

  ngOnDestroy() {
    // Cleanup handled by parent component
  }

  /**
   * Initialize group in parent form if it doesn't exist
   */
  private initializeGroupIfNeeded(): void {
    if (!this.field?.Group) return;

    const parsed = this.parseGroupPath(this.field.Group);
    if (parsed.parent && !this.form.get(parsed.parent)) {
      // Create the group array if it doesn't exist
      const groupArray = this.fb.array([this.createGroup(parsed.parent)]);
      this.form.addControl(parsed.parent, groupArray);
    }
  }

  /**
   * Parse group path to get parent and child group names
   * @param groupPath - The group path (e.g., "type|field")
   * @returns Object with parent and child group names
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    // Trim the entire groupPath first to handle trailing spaces
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Create a new FormGroup for the specified group
   * @param groupName - Name of the group to create
   * @returns FormGroup with all fields for this group
   */
  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getFieldsForGroupPath(groupName).forEach((field) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getChildGroups(groupName);
      childGroups.forEach((childGroup) => {
        const nestedGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, nestedGroupArray);
      });
    }

    return group;
  }

  /**
   * Helper method to add a field to a FormGroup
   */
  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  /**
   * Create a multi-field FormGroup
   * @param field - Field configuration
   * @returns FormGroup for multi-field
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Get fields that belong to a specific group
   * @param groupName - Name of the group
   * @returns Array of fields belonging to this group
   */
  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for a specific group path (supports nested groups with pipe notation)
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path
   */
  getFieldsForGroupPath(groupPath: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Get all child groups for a specific parent group
   * @param parentGroup - Name of the parent group
   * @returns Array of child group names
   */
  getChildGroups(parentGroup: string): string[] {
    const childGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent === parentGroup.trim() && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    return Array.from(childGroups);
  }

  /**
   * Get FormArray for a group
   * @param groupName - Name of the group
   * @returns FormArray for the group
   */
  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array using path notation
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   * @returns FormArray for the nested group
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = this.getGroupArray(parsed.parent);
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return this.getGroupArray(groupPath);
  }

  /**
   * Add a new group instance
   * @param groupName - Name of the group
   * @param index - Index to insert at (optional)
   */
  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }

    // Emit group change event
    this.groupValueChange.emit({ groupName, value: groupArray.value });
  }

  /**
   * Add nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addNestedGroup(groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
      const newGroup = this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }

      // Emit group change event
      this.groupValueChange.emit({ groupName: groupPath, value: nestedArray.value });
    }
  }

  /**
   * Remove a group instance
   * @param groupName - Name of the group
   * @param index - Index of the group to remove
   */
  removeGroup(groupName: string, index: number) {
    const groupArray = this.getGroupArray(groupName);
    groupArray.removeAt(index);

    // Emit group change event
    this.groupValueChange.emit({ groupName, value: groupArray.value });
  }

  /**
   * Remove a nested group instance
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to remove
   */
  removeNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    nestedArray.removeAt(index);

    // Emit group change event
    this.groupValueChange.emit({ groupName: groupPath, value: nestedArray.value });
  }

  /**
   * Clone a group instance
   * @param groupName - Name of the group
   * @param index - Index of the group to clone
   */
  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using existing method
    this.addGroup(groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values from original to cloned group
    this.copyGroupValues(groupToClone, clonedGroup);

    // Emit group change event
    this.groupValueChange.emit({ groupName, value: groupArray.value });
  }

  /**
   * Clone a nested group instance
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to clone
   */
  cloneNestedGroup(groupPath: string, parentIndex: number, index: number): void {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;

    // Step 1: Add a new empty nested group
    this.addNestedGroup(groupPath, parentIndex, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = nestedArray.at(index + 1) as FormGroup;

    // Step 3: Copy values from original to cloned group
    this.copyGroupValues(groupToClone, clonedGroup);

    // Emit group change event
    this.groupValueChange.emit({ groupName: groupPath, value: nestedArray.value });
  }

  /**
   * Copy values from one FormGroup to another (recursive for nested structures)
   * @param source - Source FormGroup
   * @param target - Target FormGroup
   */
  private copyGroupValues(source: FormGroup, target: FormGroup): void {
    Object.keys(source.controls).forEach(key => {
      const sourceControl = source.get(key);
      const targetControl = target.get(key);

      if (sourceControl && targetControl) {
        if (sourceControl instanceof FormArray && targetControl instanceof FormArray) {
          // Handle FormArray (multi-fields or nested groups)
          this.copyArrayValues(sourceControl, targetControl);
        } else if (sourceControl instanceof FormGroup && targetControl instanceof FormGroup) {
          // Handle nested FormGroup
          this.copyGroupValues(sourceControl, targetControl);
        } else {
          // Handle regular FormControl
          targetControl.setValue(sourceControl.value);
        }
      }
    });
  }

  /**
   * Copy values from one FormArray to another
   * @param source - Source FormArray
   * @param target - Target FormArray
   */
  private copyArrayValues(source: FormArray, target: FormArray): void {
    // Clear target array first
    while (target.length > 0) {
      target.removeAt(0);
    }

    // Copy each item from source to target
    for (let i = 0; i < source.length; i++) {
      const sourceItem = source.at(i);
      if (sourceItem instanceof FormGroup) {
        const newGroup = this.fb.group({});
        // Copy structure from source
        Object.keys(sourceItem.controls).forEach(key => {
          const sourceControl = sourceItem.get(key);
          if (sourceControl instanceof FormArray) {
            newGroup.addControl(key, this.fb.array([]));
          } else {
            newGroup.addControl(key, this.fb.control(''));
          }
        });
        target.push(newGroup);
        this.copyGroupValues(sourceItem, newGroup);
      }
    }
  }

  /**
   * Get multi-field FormArray
   * @param fieldName - Name of the field
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   * @returns FormArray for the multi-field
   */
  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  /**
   * Add a multi-field instance
   * @param field - Field configuration
   * @param groupIndex - Index of the group (optional)
   * @param index - Index to insert at (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

      // Emit field change event
      this.fieldValueChange.emit({ fieldName: field.fieldName, value: multiArray.value });
    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Remove a multi-field instance
   * @param fieldName - Name of the field
   * @param index - Index to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
      multiArray.removeAt(index);

      // Emit field change event
      this.fieldValueChange.emit({ fieldName, value: multiArray.value });
    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Handle field value changes from child components
   * @param event - Field value change event
   */
  onFieldValueChange(event: {fieldName: string, value: any}): void {
    // Forward the event to parent component
    this.fieldValueChange.emit(event);
  }

  /**
   * Check if this is the first field in a parent group (for rendering group headers)
   * @param field - Field to check
   * @returns True if this is the first field in the parent group
   */
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field that belongs to this parent group
    const firstFieldIndex = this.fields.findIndex((f) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === this.fields.indexOf(field);
  }

  /**
   * Track by function for Angular *ngFor optimization
   * @param index - Index
   * @param field - Field object
   * @returns Unique identifier for the field
   */
  trackByFieldName(index: number, field: any): string {
    return field.fieldName;
  }

  /**
   * Track by function for group controls
   * @param index - Index
   * @param group - FormGroup
   * @returns Unique identifier
   */
  trackByIndex(index: number, group: any): number {
    return index;
  }
}