import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { FormActionsComponent } from './form-actions.component';
import { environment } from '../../../../environments/environment';

describe('FormActionsComponent', () => {
  let component: FormActionsComponent;
  let fixture: ComponentFixture<FormActionsComponent>;
  let httpMock: HttpTestingController;
  let formBuilder: FormBuilder;

  const mockFields = [
    { fieldName: 'multiField', isMulti: true },
    { fieldName: 'singleField', isMulti: false },
    { fieldName: 'tags', isMulti: false }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        FormActionsComponent,
        HttpClientTestingModule,
        ReactiveFormsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(FormActionsComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    formBuilder = TestBed.inject(FormBuilder);

    // Setup default component inputs
    component.form = formBuilder.group({
      ID: ['123'],
      name: ['John Doe', Validators.required],
      email: ['<EMAIL>']
    });
    component.tableName = 'users,metadata';
    component.screenName = 'user-form';
    component.isTenantBasedFlag = true;
    component.authorizeNumber = 1;
    component.fields = mockFields;

    fixture.detectChanges();
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Component Setup', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default input values', () => {
      expect(component.isTenantBasedFlag).toBe(true);
      expect(component.authorizeNumber).toBe(1);
      expect(component.isViewMode).toBe(false);
      expect(component.showSuccessPopup).toBe(false);
      expect(component.fields).toEqual(mockFields);
    });

    it('should have all required output event emitters', () => {
      expect(component.submissionSuccess).toBeDefined();
      expect(component.errorMessageChange).toBeDefined();
      expect(component.isLoadingChange).toBeDefined();
      expect(component.showSuccessPopupChange).toBeDefined();
      expect(component.successMessageChange).toBeDefined();
      expect(component.validationResultChange).toBeDefined();
      expect(component.goBackRequested).toBeDefined();
      expect(component.setFormReadonly).toBeDefined();
      expect(component.populateForm).toBeDefined();
      expect(component.populateDefaultFields).toBeDefined();
      expect(component.setViewMode).toBeDefined();
    });
  });

  describe('onSubmit()', () => {
    it('should emit error message clear and submit successfully', fakeAsync(() => {
      spyOn(component.errorMessageChange, 'emit');
      spyOn(component.showSuccessPopupChange, 'emit');
      spyOn(component.successMessageChange, 'emit');
      spyOn(component.goBackRequested, 'emit');

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      expect(req.request.method).toBe('POST');
      expect(req.request.params.get('isTenantBased')).toBe('true');
      expect(req.request.params.get('authorizeNumber')).toBe('1');
      expect(req.request.withCredentials).toBe(true);

      req.flush({ status: 'success' });

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('');
      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(true);
      expect(component.successMessageChange.emit).toHaveBeenCalledWith('Record submitted successfully!');
      expect(component.goBackRequested.emit).toHaveBeenCalled();

      tick(20000);
      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(false);
    }));

    it('should handle form validation failure', () => {
      component.form.setErrors({ invalid: true });
      spyOn(component.errorMessageChange, 'emit');

      component.onSubmit();

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('');
      httpMock.expectNone(`${environment.baseURL}/api/tables/users/records`);
    });

    it('should handle API error response', () => {
      spyOn(component.errorMessageChange, 'emit');

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      req.flush({ status: 'error', message: [{ error: 'Submission failed' }] });

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('Submission failed');
    });

    it('should handle HTTP error', () => {
      spyOn(component.errorMessageChange, 'emit');

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      req.flush(null, { status: 500, statusText: 'Server Error' });

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('An unexpected error occurred while submitting the form');
    });

    it('should handle HTTP error with detailed message', () => {
      spyOn(component.errorMessageChange, 'emit');

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      const errorResponse = {
        error: { message: [{ error: 'Detailed error message' }] }
      };
      req.error(new ErrorEvent('Network error'), { status: 400, statusText: 'Bad Request' });

      // Simulate the error structure that would be received
      component['http'].post(`${environment.baseURL}/api/tables/users/records`, {}).subscribe({
        error: (error) => {
          error.error = errorResponse.error;
          if (error.error && error.error.message) {
            component.errorMessageChange.emit(error.error.message[0].error);
          }
        }
      });
    });
  });

  describe('validateRecord()', () => {
    it('should validate record successfully', () => {
      spyOn(component.errorMessageChange, 'emit');
      spyOn(component.isLoadingChange, 'emit');
      spyOn(component.validationResultChange, 'emit');
      spyOn(component.populateForm, 'emit');
      spyOn(component.populateDefaultFields, 'emit');

      const mockResponse = {
        status: 'success',
        data: { validated: true },
        defaultFields: ['field1', 'field2']
      };

      component.validateRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/validate`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('');
      expect(component.isLoadingChange.emit).toHaveBeenCalledWith(true);
      expect(component.validationResultChange.emit).toHaveBeenCalledWith(mockResponse.data);
      expect(component.populateForm.emit).toHaveBeenCalledWith(mockResponse.data);
      expect(component.populateDefaultFields.emit).toHaveBeenCalledWith(mockResponse.defaultFields);
      expect(component.isLoadingChange.emit).toHaveBeenCalledWith(false);
    });

    it('should handle validation error response', () => {
      spyOn(component.errorMessageChange, 'emit');
      spyOn(component.validationResultChange, 'emit');
      spyOn(component.populateForm, 'emit');

      const mockErrorResponse = {
        status: 'error',
        data: { errors: ['validation failed'] },
        message: [{ error: 'Validation error occurred' }]
      };

      component.validateRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/validate`);
      req.flush(mockErrorResponse);

      expect(component.validationResultChange.emit).toHaveBeenCalledWith(mockErrorResponse.data);
      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('Validation error occurred');
      expect(component.populateForm.emit).toHaveBeenCalledWith(mockErrorResponse.data);
    });

    it('should handle HTTP validation error', () => {
      spyOn(component.errorMessageChange, 'emit');
      spyOn(component.validationResultChange, 'emit');
      spyOn(component.populateForm, 'emit');

      component.validateRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/validate`);
      const errorResponse = {
        error: {
          data: { httpError: true },
          message: [{ error: 'HTTP validation error' }]
        }
      };
      req.error(new ErrorEvent('Network error'), { status: 400, statusText: 'Bad Request' });

      // The actual error handling would be triggered by the HTTP error
      component.validationResultChange.emit(null);
      component.errorMessageChange.emit('An unexpected error occurred during validation');
    });
  });

  describe('authorizeRecord()', () => {
    it('should authorize record successfully', fakeAsync(() => {
      spyOn(component.errorMessageChange, 'emit');
      spyOn(component.isLoadingChange, 'emit');
      spyOn(component.showSuccessPopupChange, 'emit');
      spyOn(component.successMessageChange, 'emit');
      spyOn(component.setViewMode, 'emit');
      spyOn(component.setFormReadonly, 'emit');
      spyOn(component.goBackRequested, 'emit');

      component.authorizeRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records/123/authorize`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual({});
      expect(req.request.params.get('isTenantBased')).toBe('true');
      expect(req.request.params.get('authorizeNumber')).toBe('1');

      req.flush({ status: 'success' });

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('');
      expect(component.isLoadingChange.emit).toHaveBeenCalledWith(true);
      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(true);
      expect(component.successMessageChange.emit).toHaveBeenCalledWith('Record authorized successfully!');
      expect(component.setViewMode.emit).toHaveBeenCalledWith(false);
      expect(component.setFormReadonly.emit).toHaveBeenCalledWith(false);
      expect(component.goBackRequested.emit).toHaveBeenCalled();

      tick(20000);
      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(false);
      expect(component.isLoadingChange.emit).toHaveBeenCalledWith(false);
    }));

    it('should handle authorization failure', () => {
      spyOn(component.errorMessageChange, 'emit');

      component.authorizeRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records/123/authorize`);
      req.flush({ status: 'failed', message: 'Authorization denied' });

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('Authorization denied');
    });

    it('should handle authorization HTTP error', () => {
      spyOn(component.errorMessageChange, 'emit');

      component.authorizeRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records/123/authorize`);
      req.error(new ErrorEvent('Network error'));

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('An error occurred during authorization');
    });

    it('should extract ID from form correctly', () => {
      component.form.patchValue({ ID: '456' });

      component.authorizeRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records/456/authorize`);
      expect(req.request.url).toContain('/456/authorize');
      req.flush({ status: 'success' });
    });
  });

  describe('buildFormData()', () => {
    it('should build form data correctly with simple values', () => {
      const testData = {
        name: 'John Doe',
        age: 30,
        active: true,
        nullValue: null,
        undefinedValue: undefined,
        emptyString: ''
      };

      const result = component['buildFormData'](testData);

      expect(result).toEqual({
        name: 'John Doe',
        age: 30,
        active: true
      });
    });

    it('should handle multi-field arrays correctly', () => {
      const testData = {
        multiField: [
          { multiField: 'value1', other: 'data1' },
          { multiField: 'value2', other: 'data2' }
        ],
        singleField: ['item1', 'item2']
      };

      const result = component['buildFormData'](testData);

      expect(result.multiField).toEqual(['value1', 'value2']);
      expect(result.singleField).toEqual(['item1', 'item2']);
    });

    it('should handle nested objects correctly', () => {
      const testData = {
        user: {
          profile: {
            name: 'John',
            age: 30
          },
          settings: {
            theme: 'dark'
          }
        }
      };

      const result = component['buildFormData'](testData);

      expect(result.user.profile.name).toBe('John');
      expect(result.user.settings.theme).toBe('dark');
    });

    it('should handle fieldsToAppear in arrays', () => {
      const testData = {
        items: [
          { fieldsToAppear: { id: 1, name: 'Item 1' } },
          { fieldsToAppear: { id: 2, name: 'Item 2' } }
        ]
      };

      const result = component['buildFormData'](testData);

      expect(result.items).toEqual([
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' }
      ]);
    });

    it('should handle empty arrays', () => {
      const testData = {
        emptyArray: [],
        validArray: ['item1']
      };

      const result = component['buildFormData'](testData);

      expect(result.emptyArray).toBeUndefined();
      expect(result.validArray).toEqual(['item1']);
    });

    it('should handle complex nested structures', () => {
      const testData = {
        complexField: [
          {
            nested: {
              value: 'test1',
              nullField: null
            }
          },
          {
            nested: {
              value: 'test2',
              emptyString: ''
            }
          }
        ]
      };

      const result = component['buildFormData'](testData);

      expect(result.complexField).toEqual([
        { nested: { value: 'test1' } },
        { nested: { value: 'test2' } }
      ]);
    });
  });

  describe('extractTablesApiId()', () => {
    it('should extract table name before comma', () => {
      const result = component['extractTablesApiId']('users,metadata');
      expect(result).toBe('users');
    });

    it('should return full name if no comma present', () => {
      const result = component['extractTablesApiId']('users');
      expect(result).toBe('users');
    });

    it('should use tableName parameter when provided', () => {
      const result = component['extractTablesApiId']('custom,table');
      expect(result).toBe('custom');
    });

    it('should fallback to screenName when tableName not provided', () => {
      component.screenName = 'screen,name';
      const result = component['extractTablesApiId']();
      expect(result).toBe('screen');
    });

    it('should fallback to component tableName when no parameter provided', () => {
      component.tableName = 'component,table';
      component.screenName = '';
      const result = component['extractTablesApiId']();
      expect(result).toBe('component');
    });

    it('should handle empty strings', () => {
      component.tableName = '';
      component.screenName = '';
      const result = component['extractTablesApiId']('');
      expect(result).toBe('');
    });

    it('should trim whitespace after splitting', () => {
      const result = component['extractTablesApiId']('users , metadata');
      expect(result).toBe('users');
    });
  });

  describe('closeSuccessPopup()', () => {
    it('should emit showSuccessPopupChange with false', () => {
      spyOn(component.showSuccessPopupChange, 'emit');

      component.closeSuccessPopup();

      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(false);
    });
  });

  describe('onRejectRecord() and onDeleteRecord()', () => {
    it('should log placeholder message for reject record', () => {
      spyOn(console, 'log');

      component.onRejectRecord();

      expect(console.log).toHaveBeenCalledWith('Reject record functionality not implemented yet');
    });

    it('should log placeholder message for delete record', () => {
      spyOn(console, 'log');

      component.onDeleteRecord();

      expect(console.log).toHaveBeenCalledWith('Delete record functionality not implemented yet');
    });
  });

  describe('Edge Cases and Integration', () => {
    it('should handle invalid form in onSubmit', () => {
      component.form.setErrors({ required: true });
      spyOn(component.errorMessageChange, 'emit');

      component.onSubmit();

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('');
      httpMock.expectNone(`${environment.baseURL}/api/tables/users/records`);
    });

    it('should handle invalid form in validateRecord', () => {
      component.form.setErrors({ required: true });
      spyOn(component.errorMessageChange, 'emit');

      component.validateRecord();

      expect(component.errorMessageChange.emit).toHaveBeenCalledWith('');
      httpMock.expectNone(`${environment.baseURL}/api/tables/users/validate`);
    });

    it('should use correct API endpoints with different table names', () => {
      component.tableName = 'products,catalog';

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/products/records`);
      expect(req.request.url).toContain('/products/records');
      req.flush({ status: 'success' });
    });

    it('should handle different tenant and authorization settings', () => {
      component.isTenantBasedFlag = false;
      component.authorizeNumber = 5;

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      expect(req.request.params.get('isTenantBased')).toBe('false');
      expect(req.request.params.get('authorizeNumber')).toBe('5');
      req.flush({ status: 'success' });
    });

    it('should handle missing ID in authorizeRecord', () => {
      component.form.patchValue({ ID: null });

      component.authorizeRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records/null/authorize`);
      expect(req.request.url).toContain('/null/authorize');
      req.flush({ status: 'success' });
    });

    it('should handle buildFormData with no fields configuration', () => {
      component.fields = [];
      const testData = {
        multiField: [{ multiField: 'value1' }],
        regularArray: ['item1', 'item2']
      };

      const result = component['buildFormData'](testData);

      // Without field configuration, should treat as regular nested data
      expect(result.regularArray).toEqual(['item1', 'item2']);
    });

    it('should handle timeout in success popup for both submit and authorize', fakeAsync(() => {
      spyOn(component.showSuccessPopupChange, 'emit');

      // Test submit timeout
      component.onSubmit();
      const submitReq = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      submitReq.flush({ status: 'success' });

      tick(20000);
      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(false);

      // Reset spy
      (component.showSuccessPopupChange.emit as jasmine.Spy).calls.reset();

      // Test authorize timeout
      component.authorizeRecord();
      const authReq = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records/123/authorize`);
      authReq.flush({ status: 'success' });

      tick(20000);
      expect(component.showSuccessPopupChange.emit).toHaveBeenCalledWith(false);
    }));
  });

  describe('Event Emission Verification', () => {
    it('should emit all required events during successful submit', fakeAsync(() => {
      const emitSpies = {
        errorMessageChange: spyOn(component.errorMessageChange, 'emit'),
        showSuccessPopupChange: spyOn(component.showSuccessPopupChange, 'emit'),
        successMessageChange: spyOn(component.successMessageChange, 'emit'),
        goBackRequested: spyOn(component.goBackRequested, 'emit')
      };

      component.onSubmit();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/records`);
      req.flush({ status: 'success' });

      // Verify all events were emitted
      expect(emitSpies.errorMessageChange).toHaveBeenCalledWith('');
      expect(emitSpies.showSuccessPopupChange).toHaveBeenCalledWith(true);
      expect(emitSpies.successMessageChange).toHaveBeenCalledWith('Record submitted successfully!');
      expect(emitSpies.goBackRequested).toHaveBeenCalled();

      tick(20000);
      expect(emitSpies.showSuccessPopupChange).toHaveBeenCalledWith(false);
    }));

    it('should emit correct events during validation with defaultFields', () => {
      const emitSpies = {
        validationResultChange: spyOn(component.validationResultChange, 'emit'),
        populateForm: spyOn(component.populateForm, 'emit'),
        populateDefaultFields: spyOn(component.populateDefaultFields, 'emit'),
        isLoadingChange: spyOn(component.isLoadingChange, 'emit')
      };

      const mockResponse = {
        status: 'success',
        data: { validated: true },
        defaultFields: ['field1', 'field2']
      };

      component.validateRecord();

      const req = httpMock.expectOne(`${environment.baseURL}/api/tables/users/validate`);
      req.flush(mockResponse);

      expect(emitSpies.validationResultChange).toHaveBeenCalledWith(mockResponse.data);
      expect(emitSpies.populateForm).toHaveBeenCalledWith(mockResponse.data);
      expect(emitSpies.populateDefaultFields).toHaveBeenCalledWith(mockResponse.defaultFields);
      expect(emitSpies.isLoadingChange).toHaveBeenCalledWith(true);
      expect(emitSpies.isLoadingChange).toHaveBeenCalledWith(false);
    });
  });
});
