@use '../../../../styles/shared.scss' as *;

/* Add styles for the popup */
.popup {
  position: fixed; 
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%; 
  max-width: 400px; 
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); 
  z-index: 100; 
}

.popup-content {
  text-align: center;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
}

.button-group {
  display: inline-flex !important;
  flex-direction: row-reverse !important;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: nowrap !important;
  align-items: center;
  white-space: nowrap;
}

.row-view .button-group,
.row-view-table .button-group,
.row-view-nested .button-group,
.nested-group-section .button-group,
.grouped-field-section .button-group {
  display: inline-flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  margin: 0;
  padding: 0;
}

@media (max-width: 768px) {
  .button-group {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .button-group {
    gap: 4px;
  }
}

.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 4px;
  margin-bottom: 10px;
}

.error-message {
  background-color: #fdd; /* Light red background */
  border: 1px solid #faa; /* Red border */
  color: #a00; /* Dark red text color */
  padding: 10px;
  margin-bottom: 10px; 
  border-radius: 4px; /* Slightly rounded corners */
}

/* Material snackbar styling */
.mat-mdc-snack-bar-container {
  &.success-snackbar {
    --mdc-snackbar-container-color: #4caf50;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }

  &.error-snackbar {
    --mdc-snackbar-container-color: #f44336;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }
}

/* Toggle View Button - Teal theme */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #004D40 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/* Submit Button - Green theme */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    background: #cccccc !important;
    color: #666666 !important;
    border-color: #cccccc !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Validate Button - Blue theme */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }
}

/* Authorize Button - Purple theme */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/* Back Button - Gray theme */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/* Reject Button - Red theme */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }
}

/* Delete Button - Dark Red theme */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8E0000 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* Responsive sizing for form action buttons */
@media (max-width: 768px) {
  .form-action-button {
    padding: 6px 12px;
    font-size: 13px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .form-action-button {
    padding: 5px 10px;
    font-size: 12px;
    gap: 6px;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }
}
