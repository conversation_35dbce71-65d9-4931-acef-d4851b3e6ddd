import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Form<PERSON>uilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { GroupedFieldsComponent } from './grouped-fields.component';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

describe('GroupedFieldsComponent', () => {
  let component: GroupedFieldsComponent;
  let fixture: ComponentFixture<GroupedFieldsComponent>;
  let formBuilder: FormBuilder;
  let mockForm: FormGroup;

  // Mock field data
  const mockFields = [
    {
      fieldName: 'type',
      Group: 'type',
      type: 'string',
      mandatory: true,
      noInput: false,
      isMulti: false,
      foreginKey: null
    },
    {
      fieldName: 'applicationName',
      Group: 'type',
      type: 'string',
      mandatory: false,
      noInput: false,
      isMulti: false,
      foreginKey: null
    },
    {
      fieldName: 'field',
      Group: 'type|field',
      type: 'string',
      mandatory: true,
      noInput: false,
      isMulti: false,
      foreginKey: null
    },
    {
      fieldName: 'allowedData',
      Group: 'type|field',
      type: 'string',
      mandatory: false,
      noInput: false,
      isMulti: true,
      foreginKey: null
    }
  ];

  const mockField = {
    fieldName: 'type',
    Group: 'type',
    type: 'string',
    mandatory: true,
    noInput: false,
    isMulti: false,
    foreginKey: null
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        GroupedFieldsComponent,
        RegularFieldComponent,
        ReactiveFormsModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(GroupedFieldsComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    // Setup mock form
    mockForm = formBuilder.group({});
    
    // Set component inputs
    component.field = mockField;
    component.form = mockForm;
    component.fields = mockFields;
    component.isViewMode = false;
    component.isRowView = false;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize group if not already present', () => {
      spyOn(component, 'initializeGroupIfNeeded').and.callThrough();
      
      component.ngOnInit();
      
      expect(component.initializeGroupIfNeeded).toHaveBeenCalled();
    });

    it('should create group array when group does not exist in form', () => {
      component.field = { Group: 'newGroup' };
      spyOn(component, 'createGroup').and.returnValue(formBuilder.group({}));
      
      component.ngOnInit();
      
      expect(mockForm.get('newGroup')).toBeTruthy();
      expect(mockForm.get('newGroup')).toBeInstanceOf(FormArray);
    });

    it('should not create group array when group already exists', () => {
      const existingGroup = formBuilder.array([formBuilder.group({})]);
      mockForm.addControl('existingGroup', existingGroup);
      component.field = { Group: 'existingGroup' };
      
      const initialLength = (mockForm.get('existingGroup') as FormArray).length;
      component.ngOnInit();
      
      expect((mockForm.get('existingGroup') as FormArray).length).toBe(initialLength);
    });
  });

  describe('Group Path Parsing', () => {
    it('should parse simple group path correctly', () => {
      const result = component.parseGroupPath('type');
      
      expect(result).toEqual({
        parent: 'type',
        child: null,
        isNested: false
      });
    });

    it('should parse nested group path correctly', () => {
      const result = component.parseGroupPath('type|field');
      
      expect(result).toEqual({
        parent: 'type',
        child: 'field',
        isNested: true
      });
    });

    it('should handle whitespace in group paths', () => {
      const result = component.parseGroupPath(' type | field ');
      
      expect(result).toEqual({
        parent: 'type',
        child: 'field',
        isNested: true
      });
    });
  });

  describe('Group Creation', () => {
    it('should create group with direct fields', () => {
      const group = component.createGroup('type');
      
      expect(group).toBeInstanceOf(FormGroup);
      expect(group.get('type')).toBeTruthy();
      expect(group.get('applicationName')).toBeTruthy();
    });

    it('should create nested group with specific path fields', () => {
      const group = component.createGroup('type|field');
      
      expect(group).toBeInstanceOf(FormGroup);
      expect(group.get('field')).toBeTruthy();
      expect(group.get('allowedData')).toBeTruthy();
    });

    it('should add nested subgroups to parent group', () => {
      const group = component.createGroup('type');
      
      expect(group.get('field')).toBeTruthy();
      expect(group.get('field')).toBeInstanceOf(FormArray);
    });
  });

  describe('Field Management', () => {
    it('should get fields for specific group', () => {
      const fields = component.getFieldsForGroup('type');
      
      expect(fields.length).toBe(2);
      expect(fields[0].fieldName).toBe('type');
      expect(fields[1].fieldName).toBe('applicationName');
    });

    it('should get fields for specific group path', () => {
      const fields = component.getFieldsForGroupPath('type|field');
      
      expect(fields.length).toBe(2);
      expect(fields[0].fieldName).toBe('field');
      expect(fields[1].fieldName).toBe('allowedData');
    });

    it('should get child groups for parent group', () => {
      const childGroups = component.getChildGroups('type');
      
      expect(childGroups).toContain('field');
      expect(childGroups.length).toBe(1);
    });
  });

  describe('Multi-field Creation', () => {
    it('should create multi-field FormGroup for single field', () => {
      const field = { fieldName: 'test', mandatory: true, noInput: false };
      const multiField = component.createMultiField(field);
      
      expect(multiField).toBeInstanceOf(FormGroup);
      expect(multiField.get('test')).toBeTruthy();
      expect(multiField.get('test')?.hasError('required')).toBe(false);
    });

    it('should create multi-field FormGroup for array of fields', () => {
      const fields = ['field1', 'field2'];
      const multiField = component.createMultiField(fields);
      
      expect(multiField).toBeInstanceOf(FormGroup);
      expect(multiField.get('field1')).toBeTruthy();
      expect(multiField.get('field2')).toBeTruthy();
    });

    it('should disable multi-field when noInput is true', () => {
      const field = { fieldName: 'test', mandatory: true, noInput: true };
      const multiField = component.createMultiField(field);
      
      expect(multiField.get('test')?.disabled).toBe(true);
    });
  });

  describe('Group Array Management', () => {
    beforeEach(() => {
      const groupArray = formBuilder.array([formBuilder.group({})]);
      mockForm.addControl('testGroup', groupArray);
    });

    it('should get group array correctly', () => {
      const groupArray = component.getGroupArray('testGroup');
      
      expect(groupArray).toBeInstanceOf(FormArray);
      expect(groupArray.length).toBe(1);
    });

    it('should add group to array', () => {
      spyOn(component, 'createGroup').and.returnValue(formBuilder.group({}));
      spyOn(component.groupValueChange, 'emit');
      
      component.addGroup('testGroup');
      
      const groupArray = component.getGroupArray('testGroup');
      expect(groupArray.length).toBe(2);
      expect(component.groupValueChange.emit).toHaveBeenCalled();
    });

    it('should insert group at specific index', () => {
      spyOn(component, 'createGroup').and.returnValue(formBuilder.group({ test: formBuilder.control('new') }));
      
      component.addGroup('testGroup', 0);
      
      const groupArray = component.getGroupArray('testGroup');
      expect(groupArray.length).toBe(2);
      expect(groupArray.at(1).get('test')?.value).toBe('new');
    });

    it('should remove group from array', () => {
      spyOn(component.groupValueChange, 'emit');
      
      component.removeGroup('testGroup', 0);
      
      const groupArray = component.getGroupArray('testGroup');
      expect(groupArray.length).toBe(0);
      expect(component.groupValueChange.emit).toHaveBeenCalled();
    });
  });

  describe('Nested Group Management', () => {
    beforeEach(() => {
      // Setup nested group structure
      const nestedGroupArray = formBuilder.array([formBuilder.group({})]);
      const parentGroup = formBuilder.group({ field: nestedGroupArray });
      const parentArray = formBuilder.array([parentGroup]);
      mockForm.addControl('type', parentArray);
    });

    it('should get nested group array correctly', () => {
      const nestedArray = component.getNestedGroupArray('type|field', 0);
      
      expect(nestedArray).toBeInstanceOf(FormArray);
      expect(nestedArray.length).toBe(1);
    });

    it('should add nested group', () => {
      spyOn(component, 'createGroup').and.returnValue(formBuilder.group({}));
      spyOn(component.groupValueChange, 'emit');
      
      component.addNestedGroup('type|field', 0);
      
      const nestedArray = component.getNestedGroupArray('type|field', 0);
      expect(nestedArray.length).toBe(2);
      expect(component.groupValueChange.emit).toHaveBeenCalled();
    });

    it('should remove nested group', () => {
      spyOn(component.groupValueChange, 'emit');
      
      component.removeNestedGroup('type|field', 0, 0);
      
      const nestedArray = component.getNestedGroupArray('type|field', 0);
      expect(nestedArray.length).toBe(0);
      expect(component.groupValueChange.emit).toHaveBeenCalled();
    });
  });

  describe('Group Cloning', () => {
    beforeEach(() => {
      const sourceGroup = formBuilder.group({
        field1: formBuilder.control('value1'),
        field2: formBuilder.control('value2')
      });
      const groupArray = formBuilder.array([sourceGroup]);
      mockForm.addControl('testGroup', groupArray);
    });

    it('should clone group with values', () => {
      spyOn(component, 'addGroup').and.callThrough();
      spyOn(component, 'createGroup').and.returnValue(formBuilder.group({
        field1: formBuilder.control(''),
        field2: formBuilder.control('')
      }));
      
      component.cloneGroup('testGroup', 0);
      
      expect(component.addGroup).toHaveBeenCalledWith('testGroup', 0);
      const groupArray = component.getGroupArray('testGroup');
      expect(groupArray.length).toBe(2);
    });
  });

  describe('Event Handling', () => {
    it('should emit field value change event', () => {
      spyOn(component.fieldValueChange, 'emit');
      const event = { fieldName: 'test', value: 'testValue' };
      
      component.onFieldValueChange(event);
      
      expect(component.fieldValueChange.emit).toHaveBeenCalledWith(event);
    });
  });

  describe('Helper Methods', () => {
    it('should identify first field in parent group', () => {
      const result = component.isFirstFieldInParentGroup(mockFields[0]);
      
      expect(result).toBe(true);
    });

    it('should not identify non-first field as first', () => {
      const result = component.isFirstFieldInParentGroup(mockFields[1]);
      
      expect(result).toBe(false);
    });

    it('should track by field name', () => {
      const result = component.trackByFieldName(0, { fieldName: 'test' });
      
      expect(result).toBe('test');
    });

    it('should track by index', () => {
      const result = component.trackByIndex(5, {});
      
      expect(result).toBe(5);
    });
  });

  describe('Component Integration', () => {
    it('should render group section when field has group', () => {
      component.ngOnInit();
      fixture.detectChanges();
      
      const groupSection = fixture.debugElement.query(By.css('.grouped-field-section'));
      expect(groupSection).toBeTruthy();
    });

    it('should display group title', () => {
      component.ngOnInit();
      fixture.detectChanges();
      
      const title = fixture.debugElement.query(By.css('h3'));
      expect(title?.nativeElement.textContent.trim()).toBe('type');
    });

    it('should render action buttons when not in view mode', () => {
      component.isViewMode = false;
      component.ngOnInit();
      fixture.detectChanges();
      
      const buttons = fixture.debugElement.queryAll(By.css('button[mat-icon-button]'));
      expect(buttons.length).toBeGreaterThan(0);
    });

    it('should not render action buttons in view mode', () => {
      component.isViewMode = true;
      component.ngOnInit();
      fixture.detectChanges();
      
      const buttons = fixture.debugElement.queryAll(By.css('button[mat-icon-button]'));
      expect(buttons.length).toBe(0);
    });
  });
});
